import * as v from "valibot";
import { toJsonSchema } from "@valibot/to-json-schema";

/**
 * Schema for cleaned description output from Gemini API
 */
const ParsedCleanedDescriptionSchema = v.object({
  cleanedText: v.pipe(
    v.string(),
    v.minLength(1, "Cleaned text cannot be empty"),
    v.maxLength(1500, "Cleaned text cannot be longer than 1500 characters"),
  ),
});

export const CleanedDescriptionSchema = v.pipe(
  v.string(),
  v.parseJson(),
  ParsedCleanedDescriptionSchema,
);

/**
 * TypeScript type for cleaned description output
 */
export type CleanedDescriptionOutput = v.InferOutput<
  typeof ParsedCleanedDescriptionSchema
>;

/**
 * JSON Schema for Gemini API structured output
 */
export const CleanedDescriptionJSONSchema = toJsonSchema(
  ParsedCleanedDescriptionSchema,
);

const ParsedTextExtractSchema = v.object({
  status: v.picklist(["success", "no_text_found", "error"]),
  content: v.string(),
  metadata: v.object({
    has_tables: v.boolean(),
    has_lists: v.boolean(),
    text_regions_count: v.pipe(v.number(), v.integer(), v.minValue(0)),
  }),
});

export const TextExtractSchema = v.pipe(
  v.string(),
  v.parseJson(),
  ParsedTextExtractSchema,
);

export type TextExtractOutput = v.InferOutput<typeof ParsedTextExtractSchema>;
