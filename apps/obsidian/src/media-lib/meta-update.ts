import type { TFile } from "obsidian";
import { Notice } from "obsidian";
import type MediaExtended from "@/mx-main";
import type { MediaInfo } from "@/def/media-info";
import { YouTubeService } from "@/api";
import { processMetadataForFrontmatter } from "@/api/utils/metadata-processor";
import { supabase } from "@/auth/supabase";
import { distinct } from "@std/collections";

export interface MetadataUpdateContext {
  plugin: MediaExtended;
}

/**
 * Check if the media is YouTube hosted media
 */
export function isYouTubeMedia(src: MediaInfo): boolean {
  return src.type === "url:hosted" && src.vid.host === "youtube";
}

/**
 * Check if user is logged in to Supabase
 */
export async function isUserLoggedIn(): Promise<boolean> {
  try {
    const { data, error } = await supabase.auth.getSession();
    return !error && !!data.session?.access_token;
  } catch {
    return false;
  }
}

/**
 * Check if metadata update is available for the given media
 */
export function canUpdateMetadata(src: MediaInfo): boolean {
  return isYouTubeMedia(src);
}

/**
 * Check if metadata update is available and user is logged in
 */
export async function isMetadataUpdateAvailable(
  src: MediaInfo,
): Promise<boolean> {
  if (!canUpdateMetadata(src)) {
    return false;
  }
  return await isUserLoggedIn();
}

import * as toast from "@/lib/toast";
import { UnauthorizedError } from "@/api/base/errors";

/**
 * Update YouTube metadata for a media note
 */
export async function updateYouTubeMetadata(
  file: TFile,
  videoId: string,
  plugin: MediaExtended,
): Promise<void> {
  try {
    const youtubeService = new YouTubeService();
    const metadata = await toast.promise(
      youtubeService.getVideoMetadata(videoId),
      {
        loading: "Fetching YouTube metadata...",
        success: "YouTube metadata fetched successfully",
        error: (error) => {
          if (error instanceof UnauthorizedError) {
            return "You need to login to get latest YouTube metadata";
          }
          return `Failed to fetch YouTube metadata: ${error.message || "Unknown error"}`;
        },
        loadingDelay: 200,
      },
    );

    if (!metadata) {
      new Notice(
        "Failed to fetch YouTube metadata. Please make sure you are logged in.",
      );
      return;
    }

    const { tags, ...meta } = processMetadataForFrontmatter(metadata);

    await plugin.app.fileManager.processFrontMatter(file, (frontmatter) => {
      Object.assign(frontmatter, meta);
      const newTags = distinct([...(frontmatter.tags ?? []), ...tags]);
      frontmatter.tags = newTags.slice(0, 15);
    });

    new Notice(`YouTube metadata updated successfully: ${file.path}`);
  } catch (error) {
    console.error("Failed to update YouTube metadata:", error);

    let errorMessage = "Failed to update YouTube metadata";
    if (error instanceof Error) {
      if (error.name === "YouTubeAuthError") {
        errorMessage =
          "Authentication failed. Please log in to update metadata.";
      } else if (error.name === "YouTubeNotFoundError") {
        errorMessage = "YouTube video not found.";
      } else if (error.name === "YouTubeNetworkError") {
        errorMessage = "Network error. Please check your connection.";
      } else {
        errorMessage = `Failed to update metadata: ${error.message}`;
      }
    }

    new Notice(errorMessage);
  }
}

/**
 * Update metadata for any supported media type
 */
export async function updateMediaMetadata(
  file: TFile,
  mediaInfo: MediaInfo,
  ctx: MetadataUpdateContext,
): Promise<void> {
  if (!canUpdateMetadata(mediaInfo)) {
    new Notice("Metadata update is only supported for YouTube videos.");
    return;
  }

  const loggedIn = await isUserLoggedIn();
  if (!loggedIn) {
    new Notice("Please log in to update YouTube metadata.");
    return;
  }

  if (mediaInfo.type === "url:hosted" && mediaInfo.vid.host === "youtube") {
    await updateYouTubeMetadata(file, mediaInfo.vid.vid, ctx.plugin);
  }
}
